{"name": "apply-goal-portal", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development ts-node-dev --poll --files --respawn src/index.ts", "build": "rimraf dist && cross-env NODE_ENV=production tsc", "start:prod": "cross-env NODE_ENV=production node dist/index.js", "seed": "chmod +x ./create-db.sh && ./create-db.sh && ts-node src/seeds/seed.ts", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix", "format": "eslint src/**/*.ts --fix", "pretty": "prettier --write \"src/**/*.ts\"", "prepare": "husky install", "test:dev": "jest --runInBand --watchAll --no-cache", "test": "jest --runInBand --no-cache", "typeorm": "typeorm-ts-node-commonjs -d ./src/DataSource.ts", "migration:generate": "npm run typeorm migration:generate", "migration:show": "npm run typeorm migration:show", "migration:run": "npm run typeorm migration:run", "migration:revert": "npm run typeorm migration:revert", "migration:create": "typeorm-ts-node-commonjs migration:create", "pre-commit": "lint-staged"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tsconfig/node16": "^16.1.3", "@types/bcryptjs": "^3.0.0", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/jsonfile": "^6.1.4", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.13.11", "@types/sequelize": "^4.28.20", "@types/supertest": "^6.0.2", "@types/swagger-ui-express": "^4.1.8", "@typescript-eslint/eslint-plugin": "^8.27.0", "@typescript-eslint/parser": "^8.27.0", "eslint": "^9.23.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-prettier": "^5.2.3", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.5.0", "prettier": "^3.5.3", "supertest": "^7.1.0", "ts-jest": "^29.2.6", "ts-node-dev": "^2.0.0", "typescript": "^5.8.2", "typescript-eslint": "^8.35.1"}, "dependencies": {"@types/bcrypt": "^5.0.2", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "express": "^4.21.2", "express-async-errors": "^3.1.1", "googleapis": "^148.0.0", "http-status-codes": "^2.3.0", "ioredis": "^5.6.0", "jsonfile": "^6.1.0", "jsonwebtoken": "^9.0.2", "minio": "^8.0.5", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "1.4.5-lts.2", "pg": "^8.14.1", "reflect-metadata": "^0.2.2", "rimraf": "^6.0.1", "sequelize": "^6.37.6", "sequelize-typescript": "^2.1.6", "slugify": "^1.6.6", "swagger-ui-express": "^5.0.1", "ts-node": "^10.9.2", "typedi": "^0.10.0", "typeorm": "^0.3.21", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "lint-staged": {"*.{js,ts}": ["pnpm run lint:fix"]}}