import express from 'express';
import Container from 'typedi';
import TagController from '../controllers/TagsController';
import { authenticate } from '../middlewares/auth';
const router = express.Router();

const tagController = Container.get(TagController);

router.post('/', authenticate, tagController.createTag);
router.get('/', authenticate, tagController.getAllTag);
router.delete('/:id', authenticate, tagController.deleteTag);
router.get('/:id', authenticate, tagController.getSingleTag);

export default router;
