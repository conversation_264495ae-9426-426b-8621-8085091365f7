import express from 'express';
import RequestValidator from '../middlewares/RequestValidator';
import { UserAddedDto, SignInDto } from '../dto/auth/user';
import { Container } from 'typedi';
import AuthController from '../controllers/AuthController';
import { authenticate, authorize } from '../middlewares/auth';

const router = express.Router();

const authController = Container.get(AuthController);

router.post(
  '/add-user',
  RequestValidator.validate(UserAddedDto),
  authenticate,
  authorize('user'),
  authController.addedNewUsers,
);
router.post('/sign-in', RequestValidator.validate(SignInDto), authController.signIn);
router.get('/test', authController.test);

export default router;
