import express from 'express';
import Container from 'typedi';
import { authenticate } from '../middlewares/auth';
import ScreenController from '../controllers/ScreenController';

const router = express.Router();

const screenController = Container.get(ScreenController);

router.post('/', authenticate, screenController.createScreen);
router.get('/', authenticate, screenController.getAllScreen);
router.delete('/:id', authenticate, screenController.deleteScreen);

export default router;
