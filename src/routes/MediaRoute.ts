import express from 'express';
import Container from 'typedi';
import { authenticate } from '../middlewares/auth';
// import MediaController from '../controllers/MediaController';

const  router=express.Router();

// const mediaController=Container.get(MediaController);

// router.post("/",authenticate,mediaController.uploadMedia);
// router.get("/",authenticate,screenController.getAllScreen);
// router.delete("/:id",authenticate,screenController.deleteScreen);

export default router;