import express from 'express';
import authRoute from './AuthRoute';
import userRoute from './UserRoutes';
import categoryRoute from './CategoryRoute';
import tagRoute from './TagRoute';
import screenRoute from './ScreenRoute';
import blogRoute from './BlogRoute';
import commentRoute from "./CommentsRoute";

const router = express.Router();

const allRoutes = [
  {
    path: '/auth',
    route: authRoute,
  },
  {
    path: '/users',
    route: userRoute,
  },
  {
    path: '/category',
    route: categoryRoute,
  },
  {
    path: '/tag',
    route: tagRoute,
  },
  {
    path: '/screen',
    route: screenRoute,
  },

  {
    path: '/blog',
    route: blogRoute,
  },
  {
    path: '/comment',
    route: commentRoute,
  },{
    path: '/media',
    route: mediaRoute,
  },
];

allRoutes.forEach((route) => {
  router.use(route.path, route.route);
});

export default router;
