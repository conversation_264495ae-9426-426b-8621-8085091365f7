import express from 'express';
import { authenticate } from '../middlewares/auth';
import BlogController from '../controllers/BlogController';

const route = express.Router();
route.post('/', authenticate, BlogController.createBlog);
route.get('/', authenticate, BlogController.getAllBlogs);
route.get('/:id', authenticate, BlogController.getBlogById);
route.delete('/:id', authenticate, BlogController.deleteBlog);

export default route;
