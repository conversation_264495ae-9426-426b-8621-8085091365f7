import express from 'express';
import { authenticate } from '../middlewares/auth';
import CategoryController from '../controllers/CategoryController';
import Container from 'typedi';
import RequestValidator from '../middlewares/RequestValidator';
import { CreateCategoryDto, UpdateCategoryDto } from '../dto/category';

const categoryController = Container.get(CategoryController);
const router = express.Router();

// Create Category
router.post(
  '/',
  authenticate, // Ensures user is authenticated
  RequestValidator.validate(CreateCategoryDto), // Validate incoming request body
  categoryController.createCategory,
);

// Get All Categories
router.get('/', authenticate, categoryController.getAllCategories);

// Get Single Category by ID
router.get('/:id', authenticate, categoryController.getCategoryById);

// Update Category by ID
router.put(
  '/:id',
  authenticate, // Ensure user is authenticated
  RequestValidator.validate(UpdateCategoryDto), // Validate the data for update
  categoryController.updateCategory,
);

// Delete Category by ID
router.delete('/:id', authenticate, categoryController.deleteCategory);

export default router;
