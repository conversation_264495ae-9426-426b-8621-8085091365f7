import express from 'express';
import Container from 'typedi';
import CommentController from '../controllers/CommentController';
import { authenticate } from '../middlewares/auth';

const router = express.Router();
const commentController = Container.get(CommentController);

router
  // Create a new comment
  .post('/', authenticate, commentController.createComment)

  // Get all comments
  .get('/:blogId', authenticate, commentController.getAllComments)

  // Get a single comment by ID
  .get('/:id', authenticate, commentController.getCommentById)

  // Update a comment by ID
  .put('/:id', authenticate, commentController.updateComment)

  // Delete a comment by ID
  .delete('/:id', authenticate, commentController.deleteComment);

// Get all comments for a specific blog post (optional)
//   .get("/blog/:blogId", authenticate, commentController.getCommentsByBlogId);

export default router;
