import { Request, Response, NextFunction } from 'express';
import { CustomError } from '../utils/response/errors/custom-error';
import { AppLogger } from '../utils/Logger';

export default class ErrorHandler {
  static handle = (err: Error, req: Request, res: Response, _next: NextFunction): Response => {
    if (err instanceof CustomError) {
      AppLogger.error(`CustomError: ${err.serializeErrors()}`);
      return res.status(err.statusCode).json({
        status: false,
        messages: err.message,
        errors: err.serializeErrors(),
      });
    }

    // Handle generic errors
    AppLogger.error(`Error: ${err.message}`);
    return res.status(500).json({
      status: false,
      messages: 'Something went wrong. Please try again later.',
      errors: { message: err.message },
    });
  };

  static initializeUnhandledException = () => {
    process.on('unhandledRejection', (reason: Error) => {
      AppLogger.error(reason.name + ' - ' + reason.message);
      AppLogger.error('UNHANDLED REJECTION! 💥 Shutting down...');
      throw reason;
    });

    process.on('uncaughtException', (err: Error) => {
      AppLogger.error(err.name + ' - ' + err.message);
      AppLogger.error('UNCAUGHT EXCEPTION! 💥 Shutting down...');
      process.exit(1);
    });
  };
}
