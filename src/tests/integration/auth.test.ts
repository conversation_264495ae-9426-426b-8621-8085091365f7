// import request from 'supertest';
// import { app } from '../../app';
// import { User } from '../../entity/User';
// import AppDataSource from '../../DataSource';

// const userRepo = AppDataSource.getRepository(User);

// describe('Authentication Tests', () => {
//     beforeAll(async () => {
//       // Ensure database connection is established before tests
//       if (!AppDataSource.isInitialized) {
//         await AppDataSource.initialize();
//       }
//     });

//     // afterAll(async () => {
//     //   // Properly close the database connection
//     //   if (AppDataSource.isInitialized) {
//     //     await AppDataSource.destroy();
//     //   }
//     // });

//     // Your test cases
//     it('should return 200 for test endpoint', async () => {
//       // Test implementation
//     });
//   });

// Ensure database connection before tests
//   beforeAll(async () => {
//     // Initialize the database connection if not already connected
//     if (!AppDataSource.isInitialized) {
//       await AppDataSource.initialize();
//     }

//     // Clear existing users before tests
//     await userRepo.clear();
//   });

//   // Test for simple endpoint
//   it('should return 200 for test endpoint', async () => {
//     await request(app)
//       .get('/api/v1/auth/test')
//       .expect(200);
//   });

// it('should register successfully', async () => {
//   const email = '<EMAIL>';
//   const password = 'password';
//   const username = 'sohel';
//   const name = 'rana';

//   const response = await request(app)
//     .post('/api/v1/auth/register')
//     .send({
//      username,
//       name ,
//       email,
//       password,
//     })
//     .expect(200);;
// });

// it('should login successfully', async () => {
//   const email = '<EMAIL>';
//   const password = 'password';

//   const response = await request(app)
//     .post('/api/v1/auth/sign-in')
//     .send({
//       email,
//       password,
//     })
//     .expect(200);
// });

// Cleanup after tests
//   afterAll(async () => {
//     // Clear users
//     await userRepo.clear();

//     // Close database connection
//     if (AppDataSource.isInitialized) {
//       await AppDataSource.destroy();
//     }
//   });
describe('Authentication Tests', () => {
  beforeAll(() => {
    console.log('Loaded ENV Variables:', {
      DB_HOST: process.env.DB_HOST,
      DB_USER: process.env.DB_USER,
      DB_PASSWORD: process.env.DB_PASSWORD,
      DB_NAME: process.env.DB_NAME,
      DB_PORT: process.env.DB_PORT,
      JWT_SECRET: process.env.JWT_SECRET,
      JWT_EXPIRATION: process.env.JWT_EXPIRATION,
    });
  });

  it('should return 200 for test endpoint', async () => {
    expect(1).toBe(1);
  });
});
