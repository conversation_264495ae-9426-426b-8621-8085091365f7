import { Request, Response } from 'express';
import { Service } from 'typedi';
import { MediaService } from "../services/MediaService";
// import { MediaDTO } from '../dtos/MediaDTO';
// import Busboy from 'busboy';
// import { Client } from 'minio';
// import sharp from 'sharp';
// import { extname } from 'path';
// import { v4 as uuidv4 } from 'uuid';

@Service()
export class MediaController {
  // private minioClient = new Client({
  //   endPoint: 'localhost',
  //   port: 9000,
  //   useSSL: false,
  //   accessKey: 'minoadmin',
  //   secretKey: 'minoadmin123',
  // });

  constructor(private readonly mediaService: MediaService) {}

  upload = async (req: Request, res: Response): Promise<void> => {
    // const busboy = new Busboy({ headers: req.headers });
    // let dto: Partial<MediaDTO> = {};

    // busboy.on('file', async (fieldname, file, filename, encoding, mimetype) => {
    //   try {
    //     const ext = extname(filename);
    //     const objectName = `${uuidv4()}${ext}`;
    //     const chunks: Buffer[] = [];

    //     file.on('data', (chunk) => chunks.push(chunk));
    //     file.on('end', async () => {
    //       const buffer = Buffer.concat(chunks);
    //       const sharpImage = sharp(buffer);
    //       const metadata = await sharpImage.metadata();
    //       const resized = await sharpImage.resize(800).toBuffer();

    //       await this.minioClient.putObject(this.BUCKET, objectName, resized);

    //       const mediaUrl = `http://localhost:9000/${this.BUCKET}/${objectName}`;

    //       dto = {
    //         ...dto,
    //         title: filename,
    //         mediaUrl,
    //         mediaType: 'image',
    //         fileType: mimetype,
    //         fileSize: buffer.length,
    //         fileRation: metadata.width && metadata.height ? metadata.width / metadata.height : 1,
    //       };
    //       const authorId=req.jwtPayload?.id;
    //       req.body.authorId=authorId;
    //       const saved = await this.mediaService.create(dto as MediaDTO);
    //       res.json(saved);
    //     });
    //   } catch (err) {
    //     res.status(500).json({ error: 'Failed to upload file' });
    //   }
    // });

    // req.pipe(busboy);
  };

  // getAll = async (_: Request, res: Response): Promise<void> => {
  //   const all = await this.mediaService.getAll();
  //   res.json(all);
  // };

  // getOne = async (req: Request, res: Response): Promise<void> => {
  //   const media = await this.mediaService.getOne(Number(req.params.id));
  //   if (!media) return res.status(404).json({ message: 'Not found' });
  //   res.json(media);
  // };

  // update = async (req: Request, res: Response): Promise<void> => {
  //   const updated = await this.mediaService.update(Number(req.params.id), req.body);
  //   res.json(updated);
  // };
}
