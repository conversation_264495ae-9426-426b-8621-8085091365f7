import { Service } from 'typedi';
import UserService from '../services/UserService';
import { Response, Request } from 'express';

@Service()
export default class UserController {
  constructor(public userService: UserService) {}
  getAllUsers = async (req: Request, res: Response): Promise<void> => {
    const response = await this.userService.getAllUsers();

    res.successResponse({ status: true, messages: 'All users list ', data: response });
  };
}
