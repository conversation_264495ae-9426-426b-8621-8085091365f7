import { Request, Response } from 'express';
import { Service } from 'typedi';
import CommentService from '../services/CommentService';
import { CreateCommentDTO, UpdateCommentDTO } from '../dto/comment';
import { plainToInstance } from 'class-transformer';
import { validateOrReject } from 'class-validator';
import BlogService from '../services/BlogServices';

@Service()
export default class CommentController {
  constructor(
    private readonly commentService: CommentService,
    private readonly blogService: BlogService,
  ) {}

  // Create Comment
  createComment = async (req: Request, res: Response): Promise<void> => {
    req.body.blogId = parseInt(req.body.blogId);

    //check  this blog is already exit or not
    await this.blogService.getBlogById(req.body?.blogId);

    // convert all data into dto format
    const dto = plainToInstance(CreateCommentDTO, req.body);
    validateOrReject(dto).catch((errors) => {
      console.log('Promise rejected (validation failed). Errors: ', errors);
    });

    console.log('PayloadDat32a', dto);
    const comment = await this.commentService.createComment(dto);
    res.successResponse({
      status: 201,
      messages: 'Comment created successfully!',
      data: comment,
    });
  };

  // Get All Comments
  getAllComments = async (req: Request, res: Response): Promise<void> => {
    const blogId = parseInt(req.params.blogId);
    const comments = await this.commentService.getAllComments(blogId);
    res.successResponse({
      status: 200,
      messages: 'Comments retrieved successfully!',
      data: comments,
    });
  };

  // Get Comment by ID
  getCommentById = async (req: Request, res: Response): Promise<void> => {
    const commentId = parseInt(req.params.id);
    const comment = await this.commentService.getCommentById(commentId);
    if (!comment) {
      res.status(404).send({
        status: 404,
        messages: 'Comment not found!',
      });
    }
    res.successResponse({
      status: 200,
      messages: 'Comment retrieved successfully!',
      data: comment,
    });
  };

  // Update Comment
  updateComment = async (req: Request, res: Response): Promise<void> => {
    const commentId = parseInt(req.params.id);
    const dto = plainToInstance(UpdateCommentDTO, req.body);
    await validateOrReject(dto);
    const updated = await this.commentService.updateComment(commentId, dto);
    res.successResponse({
      status: 200,
      messages: 'Comment updated successfully!',
      data: updated,
    });
  };

  // Delete Comment
  deleteComment = async (req: Request, res: Response): Promise<void> => {
    const commentId = parseInt(req.params.id);
    await this.commentService.deleteComment(commentId);
    res.successResponse({
      status: 200,
      messages: 'Comment deleted successfully!',
    });
  };
}
