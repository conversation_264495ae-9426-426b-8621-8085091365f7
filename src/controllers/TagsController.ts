import { Service } from 'typedi';
import { Request, Response } from 'express';
import TagService from '../services/TagServices';

@Service()
export default class TagController {
  constructor(public tagService: TagService) {}
  //Create Screen
  createTag = async (req: Request, res: Response): Promise<void> => {
    const screen = await this.tagService.createTag(req.body);
    res.successResponse({ status: 200, messages: 'Tag Create Successfully!', data: screen });
  };

  //Get all screen
  getAllTag = async (req: Request, res: Response): Promise<void> => {
    const screens = await this.tagService.getAllTags();
    res.successResponse({ status: 200, message: 'All tags list get successfully !', data: screens });
  };

  //Delete Screen
  deleteTag = async (req: Request, res: Response): Promise<void> => {
    const tagId = parseInt(req?.params?.id);
    await this.tagService.deleteTag(tagId);
    res.successResponse({ status: 200, messages: 'Tag deleted successfully !' });
  };
  //get Single Tag
  getSingleTag = async (req: Request, res: Response): Promise<void> => {
    const tagId = parseInt(req?.params?.id);
    const tag = await this.tagService.getTagById(tagId);
    res.successResponse({ status: 200, messages: 'Tag retrive successfully !', data: tag });
  };
}
