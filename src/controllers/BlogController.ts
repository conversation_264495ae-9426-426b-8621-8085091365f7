import { Request, Response, NextFunction } from 'express';
import { Container } from 'typedi';
import BlogService from '../services/BlogServices';
import { CreateBlogDTO, UpdateBlogDTO } from '../dto/blog';

const blogService = Container.get(BlogService);

export default class BlogController {
  // ✅ Create Blog
  static async createBlog(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const data: CreateBlogDTO = req.body;
      const authorId = req.jwtPayload?.id;
      req.body.authorId = authorId;
      const result = await blogService.createBlog(data);
      console.log('Final Result+++++++++++++++', result);
      res.successResponse({ status: 200, messages: 'Blog created successfully !', data: result });
    } catch (err) {
      next(err);
    }
  }

  // ✅ Get All Blogs
  static async getAllBlogs(req: Request, res: Response, next: NextFunction) {
    try {
      const result = await blogService.getAllBlogs();
      res.successResponse({ status: 200, messages: 'Blog retrive successfully !', data: result });
    } catch (err) {
      next(err);
    }
  }

  // ✅ Get Blog by ID
  static async getBlogById(req: Request, res: Response, next: NextFunction) {
    try {
      const id = Number(req.params.id);
      const result = await blogService.getBlogById(id);
      res.successResponse({ status: 200, messages: 'Blog retrive successfully !', data: result });
    } catch (err) {
      next(err);
    }
  }

  // ✅ Update Blog
  static async updateBlog(req: Request, res: Response, next: NextFunction) {
    try {
      const id = Number(req.params.id);
      const data: UpdateBlogDTO = req.body;
      const result = await blogService.updateBlog(id, data);
      res.successResponse({ status: 200, messages: 'Blog updated successfully !', data: result });
    } catch (err) {
      next(err);
    }
  }

  // ✅ Delete Blog
  static async deleteBlog(req: Request, res: Response, next: NextFunction) {
    try {
      const id = Number(req.params.id);
      const result = await blogService.deleteBlog(id);
      res.successResponse({ status: 200, messages: 'Blog deleted successfully !', data: result });
    } catch (err) {
      next(err);
    }
  }
}
