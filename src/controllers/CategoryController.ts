import { Request, Response } from 'express';
import { Service } from 'typedi';
import CategoryService from '../services/CategoryService';

@Service()
export default class CategoryController {
  constructor(public categoryService: CategoryService) {}

  // Create Category
  createCategory = async (req: Request, res: Response): Promise<void> => {
    const category = await this.categoryService.createCategory(req.body);
    res.successResponse({
      status: 200,
      messages: 'Category created successfully!',
      data: category,
    });
  };

  // Get All Categories
  getAllCategories = async (req: Request, res: Response): Promise<void> => {
    const categories = await this.categoryService.getAllCategories();
    res.successResponse({
      status: 200,
      messages: 'Categories retrieved successfully!',
      data: categories,
    });
  };
  // Get Single Category
  getCategoryById = async (req: Request, res: Response): Promise<void> => {
    const categoryId = parseInt(req.params.id);
    const category = await this.categoryService.getCategoryById(categoryId);
    if (!category) {
      res.send({
        status: 404,
        messages: 'Category not found!',
      });
    }
    res.successResponse({
      status: 200,
      messages: 'Category retrieved successfully!',
      data: category,
    });
  };

  // Update Category
  updateCategory = async (req: Request, res: Response): Promise<void> => {
    const categoryId = parseInt(req.params.id);
    const updatedCategory = await this.categoryService.updateCategory(categoryId, req.body);
    res.successResponse({
      status: 200,
      messages: 'Category updated successfully!',
      data: updatedCategory,
    });
  };
  // Delete Category
  deleteCategory = async (req: Request, res: Response): Promise<void> => {
    const categoryId = parseInt(req.params.id);
    await this.categoryService.deleteCategory(categoryId);
    res.successResponse({
      status: 200,
      messages: 'Category deleted successfully!',
    });
  };
}
