import { Service } from 'typedi';
import ScreenService from '../services/ScreenService';
import { Request, Response } from 'express';

@Service()
export default class ScreenController {
  constructor(public screenService: ScreenService) {}

  //Create Screen

  createScreen = async (req: Request, res: Response): Promise<void> => {
    const screen = await this.screenService.createScreen(req.body);

    res.successResponse({ status: 200, messages: 'Screen Create Successfully!', data: screen });
  };

  //Get all screen

  getAllScreen = async (req: Request, res: Response): Promise<void> => {
    const screens = await this.screenService.getAllScreens();
    res.successResponse({ status: 200, message: 'All screen list get successfully !', data: screens });
  };

  //Delete Screen

  deleteScreen = async (req: Request, res: Response): Promise<void> => {
    const screenId = parseInt(req?.params?.id);
    const deleteScreen = await this.screenService.deleteScreen(screenId);
    res.successResponse({ status: 200, messages: 'Screen delete successfully !' });
  };
}
