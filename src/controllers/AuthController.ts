import { Request, Response } from 'express';
import UserService from '../services/UserService';
import { Service } from 'typedi';
// import { User } from '../dto/auth/user';
import { signInData } from '../types/authTypes';

@Service()
export default class AuthController {
  constructor(public userService: UserService) {}

  // Added New users
  addedNewUsers = async (req: Request, res: Response): Promise<void> => {
    const addedByUserId = req.jwtPayload?.id;
    const response: { name: string; email: string; username: string } = await this.userService.addedNewUsers(
      req.body,
      addedByUserId,
    );
    res.successResponse({ status: true, messages: 'Users Created successfully ', data: response });
  };

  //Sigin New users
  signIn = async (req: Request, res: Response) => {
    const data: { data: signInData; token: string } = await this.userService.signIn(req.body);
    res.successResponse({ status: true, message: 'Signin successfully ', data: data.data, token: data.token });
  };

  test = (req: Request, res: Response): void => {
    // const users: any = [{ id: 1, username: '', name: '', email: 'd' }];
    res.successResponse('users');
  };
}
