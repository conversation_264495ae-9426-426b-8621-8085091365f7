import { plainToInstance } from 'class-transformer';
import { Service } from 'typedi';
import TagRepository from '../repositories/TagRepository';
import { CreateTagDTO, UpdateTagDto, TagResponseDto } from '../dto/tag';
import { NotFoundError } from '../utils/response/errors/not-found-error';
import { Tag } from '../entity/Tag';

@Service()
export default class TagService {
  constructor(private readonly tagRepository: TagRepository) {}

  // ✅ Create Tag
  createTag = async (data: CreateTagDTO): Promise<TagResponseDto> => {
    const tag = new Tag();
    tag.name = data.name;
    tag.slug = data.slug;
    tag.description = data.description;
    tag.amp = data.amp;
    const savedTag = await this.tagRepository.createTag(tag);
    return plainToInstance(TagResponseDto, savedTag, { excludeExtraneousValues: true });
  };

  // ✅ Get All Tags
  getAllTags = async (): Promise<TagResponseDto[]> => {
    const tags = await this.tagRepository.getAllTags();
    return plainToInstance(TagResponseDto, tags, { excludeExtraneousValues: true });
  };

  // ✅ Get Tag by ID
  getTagById = async (id: number): Promise<TagResponseDto> => {
    const tag = await this.tagRepository.getTagsById(id);
    if (!tag) throw new NotFoundError('Tag not found');

    return plainToInstance(TagResponseDto, tag, { excludeExtraneousValues: true });
  };

  //   // ✅ Update Tag
  updateTag = async (id: number, data: UpdateTagDto): Promise<any> => {
    // const updatedTag = await this.tagRepository.updateTag(id, data);
    // if (!updatedTag) throw new NotFoundError('Tag not found');
    // return plainToInstance(TagResponseDto, updatedTag, { excludeExtraneousValues: true });
  };

  // ✅ Delete Tag
  deleteTag = async (id: number): Promise<{ message: string }> => {
    const result = await this.tagRepository.deleteTag(id);
    if (!result) throw new NotFoundError('Tag not found');

    return { message: 'Tag deleted successfully' };
  };
}
