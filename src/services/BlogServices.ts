import { Service } from 'typedi';
import { plainToInstance } from 'class-transformer';
import BlogRepository from '../repositories/BlogRepository';
import { CreateBlogDTO, UpdateBlogDTO, BlogResponseDTO, DeleteBlogDTO } from '../dto/blog';
import { NotFoundError } from '../utils/response/errors/not-found-error';

@Service()
export default class BlogService {
  constructor(private readonly blogRepository: BlogRepository) {}

  // ✅ Create Blog
  createBlog = async (data: CreateBlogDTO): Promise<BlogResponseDTO> => {
    const newBlog = await this.blogRepository.createBlog(data);
    return plainToInstance(BlogResponseDTO, newBlog, { excludeExtraneousValues: true });
  };

  // ✅ Get All Blogs
  getAllBlogs = async (): Promise<any> => {
    const blogs = await this.blogRepository.getAllBlogs();
    return plainToInstance(BlogResponseDTO, blogs, { excludeExtraneousValues: true });
  };

  // ✅ Get Blog by ID
  getBlogById = async (id: number): Promise<BlogResponseDTO> => {
    const blog = await this.blogRepository.getBlogById(id);
    if (!blog) throw new NotFoundError('Blog not found');
    return plainToInstance(BlogResponseDTO, blog, { excludeExtraneousValues: true });
  };

  // ✅ Update Blog
  updateBlog = async (id: number, data: UpdateBlogDTO): Promise<BlogResponseDTO> => {
    const updatedBlog = await this.blogRepository.updateBlog(id, data);
    if (!updatedBlog) throw new NotFoundError('Blog not found');
    return plainToInstance(BlogResponseDTO, updatedBlog, { excludeExtraneousValues: true });
  };

  // ✅ Delete Blog
  deleteBlog = async (id: number): Promise<{ message: string }> => {
    const deleted = await this.blogRepository.deleteBlog(id);
    if (!deleted) throw new NotFoundError('Blog not found');
    return { message: 'Blog deleted successfully' };
  };
}
