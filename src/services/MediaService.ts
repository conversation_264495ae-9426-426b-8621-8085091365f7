// import { InjectRepository } from 'typeorm-typedi-extensions';
import { Service } from 'typedi';
import { CreateMediaDTO, MediaResponseDTO } from '../dto/media';
import MediaRepository from '../repositories/MediaRepository';
import { Media } from '../entity/Media';
// import { MediaRepository } from '../repositories/MediaRepository';
// import { Media } from '../entities/Media';
// import { MediaDTO } from '../dtos/MediaDTO';
// import { User } from '../entities/User';
// import { Library } from '../entities/Library';

@Service()
export class MediaService {
  constructor(
    private readonly mediaRepository: MediaRepository
  ) {}

//   async create(data: CreateMediaDTO): Promise<MediaResponseDTO> {
    // const media = this.mediaRepository.create({
    //   ...data,
    //   creator: data.creatorId ? ({ id: data.creatorId } as User) : undefined,
    //   library: data.libraryId ? ({ id: data.libraryId } as Library) : undefined,
    // });
    // return this.mediaRepository.save(media);
//   }

//   async getAll(): Promise<Media[]> {
//     return this.mediaRepository.find({ relations: ['creator', 'library'] });
//   }

//   async getOne(id: number): Promise<Media | undefined> {
//     return this.mediaRepository.findOne(id, { relations: ['creator', 'library'] });
//   }

//   async update(id: number, data: Partial<MediaDTO>): Promise<Media> {
//     await this.mediaRepository.update(id, data);
//     return this.getOne(id) as Promise<Media>;
//   }
}
