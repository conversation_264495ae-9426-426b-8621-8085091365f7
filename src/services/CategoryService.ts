import { plainToInstance } from 'class-transformer';
import { CategoryResponseDto, CreateCategoryDto, UpdateCategoryDto } from '../dto/category';
import CategoryRepository from '../repositories/CategoryRepository';
import { NotFoundError } from '../utils/response/errors/not-found-error';
import { Category } from '../entity/Category';
import { Service } from 'typedi';

@Service()
export default class CategoryService {
  constructor(public categoryRepository: CategoryRepository) {}

  createCategory = async (data: CreateCategoryDto): Promise<CategoryResponseDto> => {
    const category = new Category();
    category.name = data.name;
    category.description = data.description;
    category.slug = data.slug;
    category.amp = data.amp;
    // Check if a parent category exists before assigning
    if (data.parentId) {
      const parentCategory = await this.categoryRepository.getCategoryById(data.parentId);
      if (!parentCategory) throw new NotFoundError('Parent category not found');
      category.parentId = parentCategory;
    }

    const savedCategory = await this.categoryRepository.createCategory(category);
    return plainToInstance(CategoryResponseDto, savedCategory, { excludeExtraneousValues: true });
  };

  // ✅ Get All Categories
  getAllCategories = async (): Promise<Category[]> => {
    const categories = await this.categoryRepository.getAllCategories();
    return categories;
    // return plainToInstance(CategoryResponseDto, categories, { excludeExtraneousValues: true });
  };

  // ✅ Get Single Category by ID
  getCategoryById = async (id: number): Promise<CategoryResponseDto> => {
    const category = await this.categoryRepository.getCategoryById(id);
    if (!category) throw new NotFoundError('Category not found');

    return plainToInstance(CategoryResponseDto, category, { excludeExtraneousValues: true });
  };

  // ✅ Update Category
  updateCategory = async (id: number, data: UpdateCategoryDto): Promise<CategoryResponseDto> => {
    const updatedCategory = await this.categoryRepository.updateCategory(id, { ...data });

    if (!updatedCategory) throw new NotFoundError('Category not found');

    return plainToInstance(CategoryResponseDto, updatedCategory, { excludeExtraneousValues: true });
  };

  //   // ✅ Delete Category
  deleteCategory = async (id: number): Promise<{ message: string }> => {
    const result = await this.categoryRepository.deleteCategory(id);
    if (!result) throw new NotFoundError('Category not found');

    return { message: 'Category deleted successfully' };
  };
}
