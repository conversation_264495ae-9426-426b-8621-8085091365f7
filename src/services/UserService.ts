import { BadRequestError } from '../utils/response/errors/bad-request-error';
import { Service } from 'typedi';
import UserRepository from '../repositories/UserRepository';
import { UserDto, User as UserInterface, UserSingIn } from '../dto/auth/user';
import { User } from '../entity/User';
import { plainToClass } from 'class-transformer';
import { JwtPayload } from '../types/JwtPayload';

@Service()
export default class UserService {
  constructor(public userRepository: UserRepository) {}

  // Added new users

  addedNewUsers = async (data: UserInterface, addedByUserId: number): Promise<UserDto> => {
    const user = await this.userRepository.findByEmail(data.email);
    if (user) {
      throw new BadRequestError('This email is already Added');
    }

    const result = await this.userRepository.addedUsers(data, addedByUserId);
    return plainToClass(UserDto, result);
  };

  // SignIn new users functions
  signIn = async (singInData: UserSingIn) => {
    const user: User | null = await this.userRepository.findByEmail(singInData.email);
    if (!user) {
      throw new BadRequestError('You are not a user,Please contact  admin section');
    }
    const validPassword = await user.checkPasswordMatch(singInData.password);
    if (!validPassword) {
      throw new BadRequestError('wrong credentials');
    }
    const jwtPayload: JwtPayload = {
      id: user.id,
      username: user.username,
      name: user.name,
      email: user.email,
      role: user.role,
    };

    const token = user.createJwtToken(jwtPayload);
    const data = { username: user.username, name: user.name, email: user.email };
    return { data: data, token };
  };

  // Get all users list
  getAllUsers = async () => {
    return await this.userRepository.getAllUsers();
  };
}
