import { plainToInstance } from 'class-transformer';
import { Service } from 'typedi';
import ScreenRepository from '../repositories/ScreenRepository';
import { CreateScreenDto, UpdateScreenDto, ScreenResponseDto } from '../dto/screen';
import { NotFoundError } from '../utils/response/errors/not-found-error';
import { Screen } from '../entity/Screen';

@Service()
export default class ScreenService {
  constructor(private readonly screenRepository: ScreenRepository) {}

  // ✅ Create Screen
  createScreen = async (data: CreateScreenDto): Promise<ScreenResponseDto> => {
    const screen = new Screen();
    screen.name = data.name;
    const savedScreen = await this.screenRepository.createScreen(screen);
    return plainToInstance(ScreenResponseDto, savedScreen, { excludeExtraneousValues: true });
  };

  // ✅ Get All Screens
  getAllScreens = async (): Promise<ScreenResponseDto[]> => {
    const screens = await this.screenRepository.getAllScreens();
    return plainToInstance(ScreenResponseDto, screens, { excludeExtraneousValues: true });
  };

  // ✅ Get Screen by ID
  getScreenById = async (id: number): Promise<ScreenResponseDto> => {
    const screen = await this.screenRepository.getScreenById(id);
    if (!screen) throw new NotFoundError('Screen not found');
    return plainToInstance(ScreenResponseDto, screen, { excludeExtraneousValues: true });
  };

  // ✅ Update Screen
  //   updateScreen = async (id: number, data: UpdateScreenDto): Promise<ScreenResponseDto> => {
  //     const existingScreen = await this.screenRepository.getScreenById(id);
  //     if (!existingScreen) throw new NotFoundError('Screen not found');

  //     // Update fields if provided in the data DTO
  //     if (data.name) existingScreen.name = data.name;
  //     if (data.description) existingScreen.description = data.description;
  //     if (data.blogId) {
  //       const blog = await this.screenRepository.getBlogById(data.blogId);
  //       if (!blog) throw new NotFoundError('Blog not found');
  //       existingScreen.blog = blog;
  //     }

  //     const updatedScreen = await this.screenRepository.updateScreen(id, existingScreen);
  //     return plainToInstance(ScreenResponseDto, updatedScreen, { excludeExtraneousValues: true });
  //   };

  // ✅ Delete Screen
  deleteScreen = async (id: number): Promise<{ message: string }> => {
    const result = await this.screenRepository.deleteScreen(id);
    if (!result) throw new NotFoundError('Screen not found');

    return { message: 'Screen deleted successfully' };
  };
}
