import { Service } from 'typedi';
import { plainToInstance } from 'class-transformer';
import CommentRepository from '../repositories/CommentRepository';
import { CreateCommentDTO, UpdateCommentDTO, CommentResponseDTO } from '../dto/comment';
import { NotFoundError } from '../utils/response/errors/not-found-error';
import { CommentStatus } from '../entity/Comment';

@Service()
export default class CommentService {
  constructor(private readonly commentRepository: CommentRepository) {}

  // ✅ Create a new comment
  createComment = async (data: CreateCommentDTO): Promise<CommentResponseDTO> => {
    const created = await this.commentRepository.createComment(data);
    return plainToInstance(CommentResponseDTO, created, { excludeExtraneousValues: true });
  };

  // ✅ Get all comments (optional filter by status)
  getAllComments = async (blogId: number, status?: CommentStatus): Promise<CommentResponseDTO[]> => {
    const comments = await this.commentRepository.getAllComments(blogId, status);
    return plainToInstance(CommentResponseDTO, comments, { excludeExtraneousValues: true });
  };

  // ✅ Get comments by blog ID
  getCommentsByBlog = async (blogId: number): Promise<CommentResponseDTO[]> => {
    const comments = await this.commentRepository.getCommentsByBlog(blogId);
    return plainToInstance(CommentResponseDTO, comments, { excludeExtraneousValues: true });
  };

  // ✅ Get a single comment
  getCommentById = async (id: number): Promise<CommentResponseDTO> => {
    const comment = await this.commentRepository.getCommentById(id);
    if (!comment) throw new NotFoundError('Comment not found');
    return plainToInstance(CommentResponseDTO, comment, { excludeExtraneousValues: true });
  };

  // ✅ Update a comment
  updateComment = async (id: number, data: UpdateCommentDTO): Promise<CommentResponseDTO> => {
    const updated = await this.commentRepository.updateComment(id, data);
    if (!updated) throw new NotFoundError('Comment not found');
    return plainToInstance(CommentResponseDTO, updated, { excludeExtraneousValues: true });
  };

  // ✅ Delete a comment
  deleteComment = async (id: number): Promise<{ message: string }> => {
    const deleted = await this.commentRepository.deleteComment(id);
    if (!deleted) throw new NotFoundError('Comment not found');
    return { message: 'Comment deleted successfully' };
  };
}
