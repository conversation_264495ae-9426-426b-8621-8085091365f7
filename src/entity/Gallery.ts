<<<<<<< HEAD:src/entity/Gallery.ts
import {
    Entity,
    PrimaryGeneratedColumn,
    Column,
    OneToMany,
    ManyToMany,
    JoinTable,
  } from 'typeorm';
  import { Base } from './Base';
  import { Media } from './Media';
  
  @Entity('gallery')
  export class Gallery extends Base {
    @PrimaryGeneratedColumn()
    id: number;
  
    @Column({ type: 'varchar', length: 255 })
    name: string;
  
    @OneToMany(() => Media, (media) => media.library)
    media: Media[];
  
    // @ManyToMany(() => Extension)
    // @JoinTable()
    // extensionPermission: Extension[];
  }
  
=======
import { Entity, Column, OneToMany } from 'typeorm';
import { Base } from './Base';
import { Media } from './Media';
//   import { Extension } from './Extension';

@Entity('library')
export class Library extends Base {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @OneToMany(() => Media, (media) => media.library)
  media: Media[];

  // @ManyToMany(() => Extension)
  // @JoinTable()
  // extensionPermission: Extension[];
}
>>>>>>> main:src/entity/Library.ts
