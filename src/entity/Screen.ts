import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { IsNotEmpty } from 'class-validator';
import { Blog } from './Blog';
import { Base } from './Base';

@Entity('screen')
export class Screen extends Base {
  @IsNotEmpty()
  @Column({ type: 'varchar', length: 255, unique: true })
  name: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @ManyToOne(() => Blog, (blog) => blog.screens)
  blog: Blog;
}
