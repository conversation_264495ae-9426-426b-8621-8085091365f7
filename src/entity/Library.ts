import { <PERSON><PERSON><PERSON>, Column, OneToMany } from 'typeorm';
import { Base } from './Base';
import { Media } from './Media';
//   import { Extension } from './Extension';

@Entity('library')
export class Library extends Base {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @OneToMany(() => Media, (media) => media.library)
  media: Media[];

  // @ManyToMany(() => Extension)
  // @JoinTable()
  // extensionPermission: Extension[];
}
