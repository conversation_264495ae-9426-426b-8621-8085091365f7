<<<<<<< HEAD
import {
    Entity,
    PrimaryGeneratedColumn,
    Column,
    ManyToOne,
  } from 'typeorm';
  import { IsEmail, IsNotEmpty } from 'class-validator';
  import { Base } from './Base';
  import { Blog } from './Blog';
  
  export enum CommentStatus {
    PENDING = 'pending',
    ACTIVE = 'active',
    TRASH = 'trash',
  }
  
  @Entity('comment')
  export class Comment extends Base {
    @PrimaryGeneratedColumn()
    id: number;
  
    @IsNotEmpty()
    @Column({ type: 'varchar', length: 255 })
    commentorName: string;
  
    @IsEmail()
    @Column({ type: 'varchar', length: 255 })
    email: string;
  
    @IsNotEmpty()
    @Column({ type: 'text' })
    comment: string;
  
    @Column({ type: 'text', nullable: true })
    response?: string;
  
    @Column({ type: 'enum', enum: CommentStatus, default: CommentStatus.PENDING })
    status: CommentStatus;
  
    @ManyToOne(() => Blog, (blog) => blog.comments, { onDelete: 'CASCADE' })
    blog: Blog;
  }
  
=======
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { IsEmail, IsNotEmpty } from 'class-validator';
import { Base } from './Base';
import { Blog } from './Blog';

export enum CommentStatus {
  PENDING = 'pending',
  TRASH = 'trash',
}

@Entity('comment')
export class Comment extends Base {
  @PrimaryGeneratedColumn()
  id: number;

  @IsNotEmpty()
  @Column({ type: 'varchar', length: 255 })
  commentorName: string;

  @IsEmail()
  @Column({ type: 'varchar', length: 255 })
  email: string;

  @IsNotEmpty()
  @Column({ type: 'text' })
  comment: string;

  @Column({ type: 'text', nullable: true })
  response?: string;

  @Column({ type: 'enum', enum: CommentStatus, default: CommentStatus.PENDING })
  status: CommentStatus;

  @ManyToOne(() => Blog, (blog) => blog.comments, { onDelete: 'CASCADE' })
  blog: Blog;
}
>>>>>>> main
