import { Entity, Column, PrimaryGeneratedColumn, ManyToOne } from 'typeorm';
import { Base } from './Base';
import { User } from './User';
import { Library } from './Library';

@Entity('media')
export class Media extends Base {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'varchar', length: 100 })
  mediaType: string;

  @ManyToOne(() => User, (user) => user.media, { onDelete: 'SET NULL' })
  creator: User;

  @Column({ type: 'varchar', length: 255, nullable: true })
  alternativeText: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  caption: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 500 })
  mediaUrl: string;

  @Column({ type: 'varchar', length: 100 })
  fileType: string;

  @Column({ type: 'float' })
  fileSize: number;

  @Column({ type: 'float' })
  fileRation: number;

  @ManyToOne(() => Library, (library) => library.media, { onDelete: 'SET NULL' })
  library: Library;
}
