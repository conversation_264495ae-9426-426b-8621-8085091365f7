import { BeforeInsert, Column, Entity, ManyToMany, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { Base } from './Base';
import { IsNotEmpty } from 'class-validator';
import slugify from 'slugify';
import { Blog } from './Blog';

@Entity('category')
export class Category extends Base {
  @PrimaryGeneratedColumn()
  id: number;

  @IsNotEmpty()
  @Column({ type: 'varchar', length: 255, unique: true })
  name: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  slug: string;

  @ManyToOne(() => Category, (category) => category.children, { nullable: true })
  parentId: Category | null;

  @OneToMany(() => Category, (category) => category.parentId)
  children: Category[];

  @Column({ type: 'boolean', default: false })
  amp: boolean;

  // In Category entity
  @ManyToMany(() => Blog, (blog) => blog.categorys)
  blogs: Blog[];

  @BeforeInsert()
  generateSlug() {
    if (!this.slug) {
      this.slug = slugify(this.name, { lower: true, strict: true });
    }
  }
}
