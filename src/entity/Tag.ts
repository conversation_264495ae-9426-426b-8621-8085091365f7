import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToMany, JoinTable, BeforeInsert } from 'typeorm';
import { IsNotEmpty } from 'class-validator';
import slugify from 'slugify';
import { Blog } from './Blog';
import { Base } from './Base';

@Entity('tag')
export class Tag extends Base {
  @IsNotEmpty()
  @Column({ type: 'varchar', length: 255, unique: true })
  name: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  slug: string;

  @Column({ type: 'varchar', length: 255 })
  amp: string;

  @ManyToMany(() => Blog, (blog) => blog.tags)
  @JoinTable()
  blogs?: Blog[];

  @BeforeInsert()
  generateSlug() {
    if (!this.slug || this.slug.trim() === '') {
      this.slug = slugify(this.name, { lower: true, strict: true });
    } else {
      this.slug = slugify(this.slug, { lower: true, strict: true, replacement: '-', trim: true });
    }
  }
}
