import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  BaseEntity,
  BeforeInsert,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { IsNotEmpty } from 'class-validator';
import { Tag } from './Tag';
import { User } from './User';
import { Category } from './Category';
import { Comment } from './Comment';
import { Screen } from './Screen';
import slugify from 'slugify';
import { Base } from './Base';

@Entity('blog')
export class Blog extends Base {
  @IsNotEmpty()
  @Column({ type: 'varchar', length: 255, unique: true })
  title: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  slug: string;

  @Column({ type: 'text', nullable: true })
  coverImg?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  coverImgText?: string;

  @Column({ type: 'text', nullable: true })
  excert?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  seoTitle?: string;

  @Column({ type: 'text', nullable: true })
  seoMetaKey?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  seoMetaDes?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  videoUrl?: string;

  @Column({ type: 'boolean', default: true })
  visibility?: boolean;

  @Column({ type: 'date', nullable: true })
  publishOn?: Date;

  @Column({ type: 'float', nullable: true })
  seoScore?: number;

  @ManyToMany(() => Category, (category) => category.blogs)
  @JoinTable()
  categorys: Category[];

  @ManyToMany(() => Tag, (tag) => tag.blogs, { nullable: true })
  tags: Tag[];

  @OneToMany(() => Comment, (comment) => comment.blog, { nullable: true })
  comments: Comment[];

  @OneToMany(() => Screen, (screen) => screen.blog, { nullable: true })
  screens: Screen[];

  @ManyToOne(() => User, (user) => user.blogs, { nullable: false })
  author: User;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @BeforeInsert()
  generateSlug() {
    this.slug = slugify(this.title, { lower: true, strict: true });
  }
}
