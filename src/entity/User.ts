import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, ManyToOne } from 'typeorm';
import { Length, IsEmail, IsNotEmpty, IsJSON, IsArray, IsBoolean } from 'class-validator';
import jwt from 'jsonwebtoken';
import { JwtPayload } from '../types/JwtPayload';
import bcrypt from 'bcryptjs';
import { Base } from './Base';
import { UserRole } from '../types/authTypes';
import { Blog } from './Blog';
import { Media } from './Media';

@Entity('users')
export class User extends Base {
  @IsNotEmpty()
  @Column({ type: 'varchar', length: 100, unique: true }) // Unique username
  username: string;

  @Column({ type: 'varchar', length: 100, nullable: true }) // Nullable name
  name?: string;

  @Column({ type: 'varchar', length: 255, nullable: true }) // Nullable website
  website?: string;

  @IsJSON()
  @Column({ type: 'jsonb', nullable: true, default: {} }) // Default empty JSON object
  socialLink?: Record<string, string>;

  @IsArray()
  @Column({ type: 'simple-array', nullable: true, default: [] }) // Default empty array
  language?: string[];

  @IsNotEmpty()
  @Length(3, 20)
  @IsEmail()
  @Column({ type: 'varchar', length: 100, unique: true })
  email: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  password?: string;

  @IsBoolean()
  @Column({ type: 'boolean', default: true })
  sendNotification: boolean;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER,
  })
  role: UserRole;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'addedBy' })
  addedBy?: User;

  @OneToMany(() => Blog, (blog) => blog.id)
  blogs: Blog[];
  @OneToMany(() => Media, (media) => media.creator)
  media: Media[];

  async hashPassword() {
    if (this.password) {
      this.password = await bcrypt.hash(this.password, 10);
    }
  }

  async checkPasswordMatch(unEncryptedPassword: string) {
    return await bcrypt.compare(unEncryptedPassword, this.password);
  }

  createJwtToken(payload: JwtPayload) {
    if (!process.env.JWT_SECRET) {
      throw new Error('JWT_SECRET is not defined');
    }
    if (!process.env.JWT_EXPIRATION) {
      throw new Error('JWT_EXPIRATION is not defined');
    }
    return jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRATION as string,
    });
  }
}
