import { IsEmail, <PERSON>Enum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { CommentStatus } from '../entity/Comment'; // Adjust the path to your project structure
import { Expose, Type } from 'class-transformer';

// Create DTO
export class CreateCommentDTO {
  @IsNotEmpty()
  @IsString()
  commentorName: string;

  @IsEmail()
  email: string;

  @IsNotEmpty()
  @IsString()
  comment: string;

  @IsNotEmpty()
  @IsNumber()
  blogId: string;
}

// Update DTO (full or partial update)
export class UpdateCommentDTO {
  @IsOptional()
  @IsString()
  response?: string;

  @IsOptional()
  @IsEnum(CommentStatus)
  status?: CommentStatus;
}

// Update status only
export class UpdateCommentStatusDTO {
  @IsNotEmpty()
  @IsEnum(CommentStatus)
  status: CommentStatus;
}

// Delete DTO (optional if deleting via body)
export class DeleteCommentDTO {
  @IsNotEmpty()
  @IsNumber()
  id: number;
}

// Response DTO (optional, for shaping API response)
export class CommentResponseDTO {
  @Expose()
  id: number;

  @Expose()
  commentorName: string;

  @Expose()
  email: string;

  @Expose()
  comment: string;

  @Expose()
  response?: string;

  @Expose()
  status: CommentStatus;

  @Expose()
  blogId: number;

  @Expose()
  @Type(() => Date)
  createdAt: Date;

  @Expose()
  @Type(() => Date)
  updatedAt: Date;
}
