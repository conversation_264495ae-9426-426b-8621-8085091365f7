import { Expose, Type } from 'class-transformer';
import { IsArray, IsBoolean, IsDateString, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { UserDto } from './auth/user';
import { CategoryResponseDto } from './category';
import { TagResponseDto } from './tag';
import { ScreenResponseDto } from './screen';

// ✅ Create DTO
export class CreateBlogDTO {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  slug?: string;

  @IsOptional()
  @IsString()
  coverImg?: string;

  @IsOptional()
  @IsString()
  coverImgText?: string;

  @IsOptional()
  @IsString()
  excert?: string;

  @IsOptional()
  @IsString()
  seoTitle?: string;

  @IsOptional()
  @IsString()
  seoMetaKey?: string;

  @IsOptional()
  @IsString()
  seoMetaDes?: string;

  @IsOptional()
  @IsString()
  videoUrl?: string;

  @IsOptional()
  @IsBoolean()
  visibility?: boolean;

  @IsOptional()
  @IsDateString()
  publishOn?: Date;

  @IsOptional()
  @IsNumber()
  seoScore?: number;

  @IsNotEmpty()
  @IsNumber()
  authorId: number;

  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  tagIds?: number[];

  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  categoryIds?: number[];

  @IsOptional()
  @IsNumber({}, { each: true })
  @IsArray()
  screenIds?: number[];
}

// ✏️ Update DTO
export class UpdateBlogDTO {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  slug?: string;

  @IsOptional()
  @IsString()
  coverImg?: string;

  @IsOptional()
  @IsString()
  coverImgText?: string;

  @IsOptional()
  @IsString()
  excert?: string;

  @IsOptional()
  @IsString()
  seoTitle?: string;

  @IsOptional()
  @IsString()
  seoMetaKey?: string;

  @IsOptional()
  @IsString()
  seoMetaDes?: string;

  @IsOptional()
  @IsString()
  videoUrl?: string;

  @IsOptional()
  @IsBoolean()
  visibility?: boolean;

  @IsOptional()
  @IsDateString()
  publishOn?: Date;

  @IsOptional()
  @IsNumber()
  seoScore?: number;

  @IsOptional()
  @IsNumber()
  authorId?: number;

  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  tagIds?: number[];

  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  categoryIds?: number[];

  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  screenIds?: number[];
}

//  Delete DTO
export class DeleteBlogDTO {
  @IsNotEmpty()
  @IsNumber()
  id: number;
}

// 📤 Response DTO
export class BlogResponseDTO {
  @Expose()
  id: number;

  @Expose()
  title: string;

  @Expose()
  description?: string;

  @Expose()
  slug: string;

  @Expose()
  coverImg?: string;

  @Expose()
  coverImgText?: string;

  @Expose()
  excert?: string;

  @Expose()
  seoTitle?: string;

  @Expose()
  seoMetaKey?: string;

  @Expose()
  seoMetaDes?: string;

  @Expose()
  videoUrl?: string;

  @Expose()
  visibility?: boolean;

  @Expose()
  publishOn?: Date;

  @Expose()
  seoScore?: number;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;

  @Expose()
  @Type(() => UserDto)
  author: UserDto;

  @Expose({ name: 'categorys' }) // If your entity property is still `categorys`
  @Type(() => CategoryResponseDto)
  categories: CategoryResponseDto[];

  @Expose()
  @Type(() => TagResponseDto)
  tags: TagResponseDto[];

  @Expose()
  @Type(() => ScreenResponseDto)
  screens: ScreenResponseDto[];
}
