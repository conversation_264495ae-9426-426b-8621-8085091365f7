import { Expose } from 'class-transformer';
import {
    IsNotEmpty,
    IsOptional,
    IsString,
    IsNumber,
    IsUrl,
    IsEnum,
  } from 'class-validator';
  
  // Create DTO
  export class CreateMediaDTO {
    @IsNotEmpty()
    @IsString()
    title: string;
  
    @IsNotEmpty()
    @IsString()
    mediaType: string;
  
    @IsOptional()
    @IsString()
    alternativeText?: string;
  
    @IsOptional()
    @IsString()
    caption?: string;
  
    @IsOptional()
    @IsString()
    description?: string;
  
    @IsNotEmpty()
    @IsUrl()
    mediaUrl: string;
  
    @IsNotEmpty()
    @IsString()
    fileType: string;
  
    @IsNotEmpty()
    @IsNumber()
    fileSize: number;
  
    @IsNotEmpty()
    @IsNumber()
    fileRatio: number;
  
    @IsNotEmpty()
    @IsNumber()
    creatorId: number;
  
    // @IsOptional()
    // @IsNumber()
    // libraryId?: number;
  }
  
  // Update DTO
  export class UpdateMediaDTO {
    @IsOptional()
    @IsString()
    title?: string;
  
    @IsOptional()
    @IsString()
    mediaType?: string;
  
    @IsOptional()
    @IsString()
    alternativeText?: string;
  
    @IsOptional()
    @IsString()
    caption?: string;
  
    @IsOptional()
    @IsString()
    description?: string;
  
    @IsOptional()
    @IsUrl()
    mediaUrl?: string;
  
    @IsOptional()
    @IsString()
    fileType?: string;
  
    @IsOptional()
    @IsNumber()
    fileSize?: number;
  
    @IsOptional()
    @IsNumber()
    fileRation?: number;
  
    @IsOptional()
    @IsNumber()
    creatorId?: number;
  
    @IsOptional()
    @IsNumber()
    libraryId?: number;
  }
  
  // Delete DTO
  export class DeleteMediaDTO {
    @IsNotEmpty()
    @IsNumber()
    id: number;
  }
  
// Response DTO using @Expose()
export class MediaResponseDTO {
    @Expose()
    id: number;
  
    @Expose()
    title: string;
  
    @Expose()
    mediaType: string;
  
    @Expose()
    @IsOptional()
    alternativeText?: string;
  
    @Expose()
    @IsOptional()
    caption?: string;
  
    @Expose()
    @IsOptional()
    description?: string;
  
    @Expose()
    @IsNotEmpty()
    @IsUrl()
    mediaUrl: string;
  
    @Expose()
    @IsNotEmpty()
    @IsString()
    fileType: string;
  
    @Expose()
    @IsNotEmpty()
    @IsNumber()
    fileSize: number;
  
    @Expose()
    @IsNotEmpty()
    @IsNumber()
    fileRation: number;
  
    @Expose()
    creatorId: number;
  
    @Expose()
    @IsOptional()
    libraryId?: number;
  
    @Expose()
    createdAt: Date;
  
    @Expose()
    updatedAt: Date;
  }