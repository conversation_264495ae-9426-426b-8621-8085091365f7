import { Exclude, Expose, Transform } from 'class-transformer';

import { IsEmail, IsNotEmpty } from 'class-validator';

export class UserAddedDto {
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  password: string;

  @IsNotEmpty()
  name: string;

  @IsNotEmpty()
  username: string;
}

export class SignInDto {
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  password: string;
}

@Exclude()
export class UserDto {
  @Expose()
  id: number;

  @Expose()
  username: string;

  @Expose()
  name: string;

  @Expose()
  email: string;

  @Expose()
  website: string;

  @Expose()
  language: string[]; // Array of languages

  @Expose()
  socialLink: Record<string, string>; // Key-value object for social links

  @Expose()
  sendNotification: boolean; // Boolean flag for notifications

  @Expose()
  role: string;

  // Exclude password from the response
  @Exclude()
  password: string;
  // Transform addedBy to return only `id`, `username`, and `email`
  @Expose()
  @Transform(({ value }) => {
    if (!value) return null;
    return {
      id: value.id,
      username: value.username,
      email: value.email,
    };
  })
  addedBy: Partial<UserDto>;
}

export interface User {
  id: number;
  username: string;
  name: string;
  email: string;
  password: string;
  website?: string;
  language?: string[]; // Array of languages
  socialLink?: Record<string, string>; // Key-value object for social links
  sendNotification?: boolean; // Boolean flag for notifications
  addedBy?: number; // ID of the user who added this user
}

export interface UserSingIn {
  email: string;
  password: string;
}
