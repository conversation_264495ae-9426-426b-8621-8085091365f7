import { Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateScreenDto {
  @IsNotEmpty()
  name: string;

  @IsOptional()
  blogId?: number; // Optional: blog ID to associate with this screen
}

export class UpdateScreenDto {
  @IsOptional()
  name?: string;

  @IsOptional()
  blogId?: number; // Optional: blog ID to associate with this screen
}

export class ScreenResponseDto {
  @Expose()
  id: number;

  @Expose()
  name: string;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}
