import { Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsBoolean, IsString } from 'class-validator';

export class CreateTagDTO {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  slug?: string;

  @IsOptional()
  @IsString()
  amp: string;
}
export class UpdateTagDto {
  @IsOptional()
  name?: string;

  @IsOptional()
  description?: string;

  @IsOptional()
  slug?: string;

  @IsOptional()
  @IsBoolean()
  amp?: boolean;
}

export class TagResponseDto {
  @Expose()
  id: number;

  @Expose()
  name: string;

  @Expose()
  description?: string;

  @Expose()
  slug: string;

  @Expose()
  amp: string;

  // @Expose()
  // createdAt: Date;

  // @Expose()
  // updatedAt: Date;
}
