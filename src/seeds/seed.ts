import AppDataSource from '../DataSource';
import { User } from '../entity/User';
import { UserRole } from '../types/authTypes';
import bcrypt from 'bcryptjs';

const seedDatabase = async () => {
  try {
    await AppDataSource.initialize();
    console.log('Database connected successfully!');

    const userRepository = AppDataSource.getRepository(User);

    // Check if admin user already exists
    const existingAdmin = await userRepository.findOne({
      where: { email: '<EMAIL>' },
    });

    if (!existingAdmin) {
      const adminUser = userRepository.create({
        username: 'admin',
        email: '<EMAIL>',
        password: await bcrypt.hash('password', 10), // Hash password before saving
        role: UserRole.ADMIN, // Assuming UserRole.ADMIN exists
      });

      await userRepository.save(adminUser);
      console.log('Admin user created successfully!');
    } else {
      console.log('Admin user already exists, skipping creation.');
    }

    await AppDataSource.destroy();
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

// Run the seeding script
seedDatabase();
