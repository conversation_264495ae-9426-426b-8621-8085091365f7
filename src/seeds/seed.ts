import { DataSource } from 'typeorm';
import { User } from '../entity/User';
import { Blog } from '../entity/Blog';
import { Category } from '../entity/Category';
import { Comment } from '../entity/Comment';
import { Library } from '../entity/Library';
import { Media } from '../entity/Media';
import { Screen } from '../entity/Screen';
import { Tag } from '../entity/Tag';
import { Base } from '../entity/Base';
import { UserRole } from '../types/authTypes';
import bcrypt from 'bcryptjs';
import Config from '../config/Config';

// Create DataSource for seeding with explicit entity imports
const SeedDataSource = new DataSource({
  host: Config.host || 'localhost',
  username: Config.username || 'postgres',
  password: Config.password || 'postgres123',
  database: Config.database || 'applyGoalPortal',
  type: 'postgres',
  port: parseInt(Config.db_port) || 5432,
  logging: false,
  synchronize: true,
  entities: [Base, User, Blog, Category, Comment, Library, Media, Screen, Tag],
});

const seedDatabase = async () => {
  try {
    await SeedDataSource.initialize();
    console.log('Database connected successfully!');

    const userRepository = SeedDataSource.getRepository(User);

    // Check if admin user already exists
    const existingAdmin = await userRepository.findOne({
      where: { email: '<EMAIL>' },
    });

    if (!existingAdmin) {
      const adminUser = userRepository.create({
        username: 'admin',
        name: 'Administrator',
        email: '<EMAIL>',
        password: await bcrypt.hash('password', 10), // Hash password before saving
        role: UserRole.ADMIN,
      });

      await userRepository.save(adminUser);
      console.log('Admin user created successfully!');
    } else {
      console.log('Admin user already exists, skipping creation.');
    }

    await SeedDataSource.destroy();
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

// Run the seeding script
seedDatabase();
