import { User } from '../entity/User';
import { User as UserInterface } from '../dto/auth/user';
import { Service } from 'typedi';
import AppDataSource from '../DataSource';
@Service()
export default class UserRepository {
  userRepo = AppDataSource.getRepository(User);

  // Added new users
  addedUsers = async (userData: UserInterface, addedByUserId: number): Promise<User> => {
    const user = new User();
    user.email = userData.email;
    user.username = userData.username;
    user.name = userData.name;
    user.password = userData.password;
    user.website = userData.website;
    user.language = userData.language || []; // Default empty array
    user.socialLink = userData.socialLink || {}; // Default empty object
    user.sendNotification = userData.sendNotification ?? true; // Default true

    await user.hashPassword();
    // Set `addedBy` if provided
    if (addedByUserId) {
      user.addedBy = await this.userRepo.findOneBy({ id: addedByUserId });
    }

    return await this.userRepo.save(user);
  };

  findByEmail = async (email: string): Promise<User | null> => {
    return await this.userRepo.findOneBy({ email: email });
  };

  getAllUsers = async (): Promise<User[]> => {
    return await this.userRepo.find();
  };
}
