import { Repository } from 'typeorm';
import { Tag } from '../entity/Tag';
import AppDataSource from '../DataSource';
import { CreateTagDTO, TagResponseDto } from '../dto/tag';
import { Service } from 'typedi';

@Service()
export default class TagRepositroy {
  private tagRepo: Repository<Tag>;
  constructor() {
    this.tagRepo = AppDataSource.getRepository(Tag);
  }

  async createTag(data: CreateTagDTO): Promise<TagResponseDto> {
    const newTag = this.tagRepo.create(data);

    const savedTag = await this.tagRepo.save(newTag);
    return savedTag;
  }

  async getAllTags(): Promise<Tag[]> {
    const categories = await this.tagRepo.find();
    return categories;
  }

  async getTagsById(id: number): Promise<TagResponseDto> {
    return await this.tagRepo.findOne({
      where: { id },
    });
  }
  async deleteTag(id: number): Promise<boolean> {
    const result = await this.tagRepo.delete(id);
    return result.affected ? true : false;
  }
}
