import { Repository } from 'typeorm';
import AppDataSource from '../DataSource';
import { Screen } from '../entity/Screen';
import { Blog } from '../entity/Blog';
import { Service } from 'typedi';
import { CreateScreenDto, UpdateScreenDto } from '../dto/screen';

@Service()
export default class ScreenRepository {
  private screenRepo: Repository<Screen>;
  private blogRepo: Repository<Blog>;

  constructor() {
    this.screenRepo = AppDataSource.getRepository(Screen);
    this.blogRepo = AppDataSource.getRepository(Blog);
  }

  // ✅ Create new screen
  async createScreen(data: CreateScreenDto): Promise<Screen> {
    const newScreen = this.screenRepo.create(data);
    return await this.screenRepo.save(newScreen);
  }

  //   ✅ Get all screens with optional relations (e.g., Blog)
  async getAllScreens(): Promise<Screen[]> {
    return await this.screenRepo.find({
      relations: ['blog'],
      order: { createdAt: 'DESC' },
    });
  }

  //   ✅ Get screen by ID
  async getScreenById(id: number): Promise<Screen | null> {
    return await this.screenRepo.findOne({
      where: { id },
      relations: ['blog'],
    });
  }

  //   ✅ Update screen by ID
  async updateScreen(id: number, data: UpdateScreenDto): Promise<Screen | null> {
    const screen = await this.getScreenById(id);
    if (!screen) return null;

    Object.assign(screen, data);
    return await this.screenRepo.save(screen);
  }

  //   ✅ Delete screen by ID
  async deleteScreen(id: number): Promise<boolean> {
    const result = await this.screenRepo.delete(id);
    return result.affected ? true : false;
  }
}
