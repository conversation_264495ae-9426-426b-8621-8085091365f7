import { Repository } from 'typeorm';
import { Service } from 'typedi';
import AppDataSource from '../DataSource';
import { Media } from '../entity/Media';
import { Blog } from '../entity/Blog';
import { User } from '../entity/User';
import { CreateMediaDTO, MediaResponseDTO, UpdateMediaDTO } from '../dto/media';

@Service()
export default class MediaRepository {
  private mediaRepo: Repository<Media>;
  private blogRepo: Repository<Blog>;
  private userRepo: Repository<User>;

  constructor() {
    this.mediaRepo = AppDataSource.getRepository(Media);
    this.blogRepo = AppDataSource.getRepository(Blog);
    this.userRepo = AppDataSource.getRepository(User);
  }

  // ✅ Create new media
  async createMedia(data: CreateMediaDTO): Promise<any> {
    const media = this.mediaRepo.create(data);

    if (data.creatorId) {
      const user = await this.userRepo.findOne({ where: { id: data.creatorId } });
      if (user) media.creator = user;
    }

    return await this.mediaRepo.save(media);
  }

  // ✅ Get all media files
//   async getAllMedia(): Promise<Media[]> {
//     return await this.mediaRepo.find({ relations: ['blog', 'creator'] });
//   }

  // ✅ Get single media by ID
//   async getMediaById(id: number): Promise<Media | null> {
//     return await this.mediaRepo.findOne({ where: { id }, relations: ['blog', 'creator'] });
//   }

  // ✅ Update media by ID
//   async updateMedia(id: number, data: UpdateMediaDto): Promise<Media | null> {
//     const media = await this.getMediaById(id);
//     if (!media) return null;

//     Object.assign(media, data);

//     if (data.blogId) {
//       const blog = await this.blogRepo.findOne({ where: { id: data.blogId } });
//       if (blog) media.blog = blog;
//     }

//     if (data.creatorId) {
//       const user = await this.userRepo.findOne({ where: { id: data.creatorId } });
//       if (user) media.creator = user;
//     }

//     return await this.mediaRepo.save(media);
//   }

  // ✅ Delete media
//   async deleteMedia(id: number): Promise<boolean> {
//     const result = await this.mediaRepo.delete(id);
//     return result.affected !== 0;
//   }
}
