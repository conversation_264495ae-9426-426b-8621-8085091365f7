import { Repository } from 'typeorm';
import AppDataSource from '../DataSource';
import { Category } from '../entity/Category';
import { Service } from 'typedi';
import { UpdateCategoryDto } from '../dto/category';

@Service()
export default class CategoryRepository {
  private categoryRepo: Repository<Category>;
  constructor() {
    this.categoryRepo = AppDataSource.getRepository(Category);
  }

  // Create tree structure category data
  private buildCategoryTree(categories: Category[], parentId: number | null = null): Category[] {
    return categories
      .filter((category) => (category.parentId ? category.parentId.id : null) === parentId)
      .map((category) => {
        category.children = this.buildCategoryTree(categories, category.id);
        return category;
      });
  }

  // ✅ Create a new category
  async createCategory(data: Partial<Category>): Promise<Category> {
    const newCategory = this.categoryRepo.create(data);
    return await this.categoryRepo.save(newCategory);
  }

  // ✅ Get all categories with their children
  async getAllCategories(): Promise<Category[]> {
    const categories = await this.categoryRepo.find({
      relations: ['parentId', 'children'],
    });

    return this.buildCategoryTree(categories);
  }

  // ✅ Get single category by ID
  async getCategoryById(id: number): Promise<Category | null> {
    return await this.categoryRepo.findOne({
      where: { id },
      relations: ['children'],
    });
  }

  // ✅ Update category
  async updateCategory(id: number, data: Partial<UpdateCategoryDto>): Promise<Category | null> {
    const category = await this.getCategoryById(id);
    if (!category) return null;

    Object.assign(category, data);
    return await this.categoryRepo.save(category);
  }

  // ✅ Delete category
  async deleteCategory(id: number): Promise<boolean> {
    const result = await this.categoryRepo.delete(id);
    return result.affected ? true : false;
  }
}
