import { Repository, In } from 'typeorm';
import AppDataSource from '../DataSource';
import { Blog } from '../entity/Blog';
import { Category } from '../entity/Category';
import { Tag } from '../entity/Tag';
import { User } from '../entity/User';
import { Service } from 'typedi';
import { CreateBlogDTO, UpdateBlogDTO } from '../dto/blog';
import { Screen } from '../entity/Screen';

@Service()
export default class BlogRepository {
  private blogRepo: Repository<Blog>;
  private tagRepo: Repository<Tag>;
  private categoryRepo: Repository<Category>;
  private userRepo: Repository<User>;
  private screenRepo: Repository<Screen>;

  constructor() {
    this.blogRepo = AppDataSource.getRepository(Blog);
    this.tagRepo = AppDataSource.getRepository(Tag);
    this.categoryRepo = AppDataSource.getRepository(Category);
    this.userRepo = AppDataSource.getRepository(User);
    this.screenRepo = AppDataSource.getRepository(Screen);
  }

<<<<<<< HEAD
  // 🔁 Reusable Helpers
  private async assignAuthor(blog: Blog, authorId: number) {
    const author = await this.userRepo.findOneBy({ id: authorId });
=======
  // ✅ Create new blog
  async createBlog(data: CreateBlogDTO): Promise<Blog> {
    console.log('Blog Data', data);
    const blog = this.blogRepo.create(data);

    // Set author
    const author = await this.userRepo.findOneBy({ id: data.authorId });
>>>>>>> main
    if (!author) throw new Error('Author not found');
    blog.author = author;
  }

  private async assignTags(blog: Blog, tagIds?: number[]) {
    if (tagIds?.length) {
      blog.tags = await this.tagRepo.findBy({ id: In(tagIds) });
    } else {
      blog.tags = [];
    }
  }

  private async assignCategories(blog: Blog, categoryIds?: number[]) {
    if (categoryIds?.length) {
      blog.categories = await this.categoryRepo.findBy({ id: In(categoryIds) });
    } else {
      blog.categories = [];
    }
  }

  private async assignScreens(blog: Blog, screenIds?: number[]) {
    if (screenIds?.length) {
      blog.screens = await this.screenRepo.findBy({ id: In(screenIds) });
    } else {
      blog.screens = [];
    }
  }
  // ✅ Create new blog
  async createBlog(data: CreateBlogDTO): Promise<Blog> {
    const blog = this.blogRepo.create(data);

    await this.assignAuthor(blog, data.authorId);
    await this.assignTags(blog, data.tagIds);
    await this.assignCategories(blog, data.categoryIds);
    await this.assignScreens(blog, data.screenIds);

    return await this.blogRepo.save(blog);
  }

  // ✅ Update blog
  async updateBlog(id: number, data: UpdateBlogDTO): Promise<Blog | null> {
    const blog = await this.getBlogById(id);
    if (!blog) return null;

    Object.assign(blog, data);

    if (data.authorId) {
      await this.assignAuthor(blog, data.authorId);
    }

<<<<<<< HEAD
    await this.assignTags(blog, data.tagIds);
    await this.assignCategories(blog, data.categoryIds);
    await this.assignScreens(blog, data.screenIds);

    return await this.blogRepo.save(blog);
=======
    // Set categories
    if (data.categoryIds?.length) {
      blog.categorys = await this.categoryRepo.findBy({ id: In(data.categoryIds) });
    }
    if (data.screenIds?.length) {
      blog.categorys = await this.categoryRepo.findBy({ id: In(data.categoryIds) });
    }

    if (data.screenIds?.length) {
      blog.screens = await this.screenRepo.findBy({ id: In(data.screenIds) });
    }

    const newBlogs = await this.blogRepo.save(blog);
    return newBlogs;
>>>>>>> main
  }

  // ✅ Get all blogs (with author, categories, tags, comments)
  async getAllBlogs(): Promise<Blog[]> {
    return await this.blogRepo.find({
      relations: ['author', 'tags', 'categorys', 'comments'],
      order: { createdAt: 'DESC' },
    });
  }

  // ✅ Get blog by ID
  async getBlogById(id: number): Promise<Blog | null> {
    return await this.blogRepo.findOne({
      where: { id },
      relations: ['author', 'tags', 'categorys', 'comments'],
    });
  }

  // ✅ Get blog by slug
  async getBlogBySlug(slug: string): Promise<Blog | null> {
    return await this.blogRepo.findOne({
      where: { slug },
      relations: ['author', 'tags', 'categorys', 'comments'],
    });
  }

  // ✅ Delete blog
  async deleteBlog(id: number): Promise<boolean> {
    const result = await this.blogRepo.delete(id);
    return result.affected ? true : false;
  }
}
