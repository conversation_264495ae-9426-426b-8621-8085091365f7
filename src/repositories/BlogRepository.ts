import { Repository, In } from 'typeorm';
import AppDataSource from '../DataSource';
import { Blog } from '../entity/Blog';
import { Category } from '../entity/Category';
import { Tag } from '../entity/Tag';
import { User } from '../entity/User';
import { Service } from 'typedi';
import { CreateBlogDTO, UpdateBlogDTO } from '../dto/blog';
import { Screen } from '../entity/Screen';

@Service()
export default class BlogRepository {
  private blogRepo: Repository<Blog>;
  private tagRepo: Repository<Tag>;
  private categoryRepo: Repository<Category>;
  private userRepo: Repository<User>;
  private screenRepo: Repository<Screen>;

  constructor() {
    this.blogRepo = AppDataSource.getRepository(Blog);
    this.tagRepo = AppDataSource.getRepository(Tag);
    this.categoryRepo = AppDataSource.getRepository(Category);
    this.userRepo = AppDataSource.getRepository(User);
    this.screenRepo = AppDataSource.getRepository(Screen);
  }

  // ✅ Create new blog
  async createBlog(data: CreateBlogDTO): Promise<Blog> {
    console.log('Blog Data', data);
    const blog = this.blogRepo.create(data);

    // Set author
    const author = await this.userRepo.findOneBy({ id: data.authorId });
    if (!author) throw new Error('Author not found');
    blog.author = author;

    // Set tags
    if (data.tagIds?.length) {
      blog.tags = await this.tagRepo.findBy({ id: In(data.tagIds) });
    }

    // Set categories
    if (data.categoryIds?.length) {
      blog.categorys = await this.categoryRepo.findBy({ id: In(data.categoryIds) });
    }
    if (data.screenIds?.length) {
      blog.categorys = await this.categoryRepo.findBy({ id: In(data.categoryIds) });
    }

    if (data.screenIds?.length) {
      blog.screens = await this.screenRepo.findBy({ id: In(data.screenIds) });
    }

    const newBlogs = await this.blogRepo.save(blog);
    return newBlogs;
  }

  // ✅ Get all blogs (with author, categories, tags, comments)
  async getAllBlogs(): Promise<Blog[]> {
    return await this.blogRepo.find({
      relations: ['author', 'tags', 'categorys', 'comments'],
      order: { createdAt: 'DESC' },
    });
  }

  // ✅ Get blog by ID
  async getBlogById(id: number): Promise<Blog | null> {
    return await this.blogRepo.findOne({
      where: { id },
      relations: ['author', 'tags', 'categorys', 'comments'],
    });
  }

  // ✅ Get blog by slug
  async getBlogBySlug(slug: string): Promise<Blog | null> {
    return await this.blogRepo.findOne({
      where: { slug },
      relations: ['author', 'tags', 'categorys', 'comments'],
    });
  }

  // ✅ Update blog
  async updateBlog(id: number, data: UpdateBlogDTO): Promise<Blog | null> {
    const blog = await this.getBlogById(id);
    if (!blog) return null;

    Object.assign(blog, data);

    if (data.authorId) {
      const author = await this.userRepo.findOneBy({ id: data.authorId });
      if (!author) throw new Error('Author not found');
      blog.author = author;
    }

    if (data.tagIds) {
      blog.tags = await this.tagRepo.findBy({ id: In(data.tagIds) });
    }

    if (data.categoryIds) {
      blog.categorys = await this.categoryRepo.findBy({ id: In(data.categoryIds) });
    }

    return await this.blogRepo.save(blog);
  }

  // ✅ Delete blog
  async deleteBlog(id: number): Promise<boolean> {
    const result = await this.blogRepo.delete(id);
    return result.affected ? true : false;
  }
}
