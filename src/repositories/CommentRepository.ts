import { Repository } from 'typeorm';
import AppDataSource from '../DataSource';
import { Comment, CommentStatus } from '../entity/Comment';
import { Service } from 'typedi';
import { CreateCommentDTO, UpdateCommentDTO } from '../dto/comment';

@Service()
export default class CommentRepository {
  private commentRepo: Repository<Comment>;

  constructor() {
    this.commentRepo = AppDataSource.getRepository(Comment);
  }

  // ✅ Create a new comment
  async createComment(data: CreateCommentDTO): Promise<Comment> {
    const newComment = this.commentRepo.create({
      commentorName: data.commentorName,
      email: data.email,
      comment: data.comment,
      blog: { id: Number(data.blogId) },
    });

    return await this.commentRepo.save(newComment);
  }

  // ✅ Get all comments (optional filter by status)
<<<<<<< HEAD
  async getAllComments(blogId: number,status:string): Promise<Comment[]> {
=======
  async getAllComments(blogId: number, status?: CommentStatus): Promise<Comment[]> {
>>>>>>> main
    return await this.commentRepo.find({
      where: {
        blog: {
          id: blogId,
        },
      },
      relations: ['blog'],
      order: { createdAt: 'DESC' },
    });
  }

  // ✅ Get comments by blog
  async getCommentsByBlog(blogId: number): Promise<Comment[]> {
    return await this.commentRepo.find({
      where: { blog: { id: blogId } },
      relations: ['blog'],
      order: { createdAt: 'DESC' },
    });
  }

  // ✅ Get single comment by ID
  async getCommentById(id: number): Promise<Comment | null> {
    return await this.commentRepo.findOne({
      where: { id },
      relations: ['blog'],
    });
  }

  // ✅ Update comment (including status or response)
  async updateComment(id: number, data: Partial<UpdateCommentDTO>): Promise<Comment | null> {
    const comment = await this.getCommentById(id);
    if (!comment) return null;

    Object.assign(comment, data);
    return await this.commentRepo.save(comment);
  }

  // ✅ Delete comment
  async deleteComment(id: number): Promise<boolean> {
    const result = await this.commentRepo.delete(id);
    return result.affected ? true : false;
  }
}
