import { CustomError } from './custom-error';

export class RequestValidationError extends CustomError {
  statusCode = 400;

  constructor(public errors: unknown) {
    super('Invalid request parameters');

    // Only because we are extending a built in class
    Object.setPrototypeOf(this, RequestValidationError.prototype);
  }

  serializeErrors(): { message: string } | Record<string, unknown> {
    if (typeof this.errors === 'object' && this.errors !== null) {
      return this.errors as Record<string, unknown>;
    }
    return { message: 'Invalid request parameters' };
  }
}
