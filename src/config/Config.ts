import dotenv from 'dotenv';
dotenv.config({ path: __dirname + `/../../.env.${process.env.NODE_ENV}` });
const config = {
  port: process.env.PORT,
  host: process.env.DB_HOST,
  username: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  db_port: process.env.DB_PORT,
  minio_accesskey: process.env.MINIO_ACCESSKEY,
  minio_secretkey: process.env.MINIO_SECRETKEY,
  minio_port:process.env.MINIO_PORT,
  minio_endpoint:process.env.MINIO_ENDPOINT,
  bucket_name:process.env.BUCKET_NAME,
};
export default config;
