#!/bin/bash

# Database connection details
DB_HOST="localhost"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="postgres123"
DB_NAME="applyGoalPortal"

# Create the database if it doesn't exist
echo "Checking if database '$DB_NAME' exists..."

export PGPASSWORD=$DB_PASSWORD
DB_EXISTS=$(psql -h $DB_HOST -U $DB_USER -p $DB_PORT -tAc "SELECT 1 FROM pg_database WHERE datname='${DB_NAME}'")

if [ "$DB_EXISTS" = "1" ]; then
  echo "✅ Database '$DB_NAME' already exists."
else
  echo "🚀 Creating database '$DB_NAME'..."
  createdb -h $DB_HOST -U $DB_USER -p $DB_PORT $DB_NAME && echo "✅ Created successfully!"
fi
